import 'package:flutter/material.dart';
import 'package:flutter_firebase/service/AuthServices/auth_services.dart';
import 'package:google_fonts/google_fonts.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final emailCTRL = TextEditingController();
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            children: [
              Text(
                "Forgot Password",
                style: GoogleFonts.getFont(
                  'Lato',
                  color: Colors.black,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.2,
                ),
              ),

              Image.asset(
                'assets/images/Illustration.png',
                height: 300,
                width: 300,
                fit: BoxFit.cover,
              ),
              Align(
                alignment: Alignment.topLeft,
                child: Text(
                  'Email',
                  style: GoogleFonts.getFont(
                    'Nunito Sans',
                    color: Colors.black,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    letterSpacing: 0.2,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              TextFormField(
                onChanged: (value) => emailCTRL.text = value,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  // Add more validation logic if needed
                  return null;
                },
                decoration: InputDecoration(
                  fillColor: Colors.white,
                  filled: true,

                  // hintText: 'Enter your email',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(9.0),
                  ),
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  labelText: 'Enter your email',
                  labelStyle: GoogleFonts.getFont(
                    'Nunito Sans',
                    color: Colors.black54,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    letterSpacing: 0.1,
                  ),
                  // prefix: Icon(Icons.email_outlined),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Image.asset(
                      'assets/icons/email.png',
                      height: 20,
                      width: 20,
                      color: Colors.black54,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    if (emailCTRL.text.isEmpty) {
                      AuthServices.showSnackBar(
                        'Please add your email',
                        context,
                      );
                    } else if (!validate(emailCTRL.text)) {
                      AuthServices.showSnackBar(
                        'Please enter your email',
                        context,
                      );
                    } else {
                      setState(() {
                        isLoading = true;
                      });
                      // Handle visibility toggle
                      AuthServices.ResetforgetPasswordsendEmail(
                        emailCTRL.text,
                        context,
                      );
                      setState(() {
                        isLoading = false;
                      });
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5),
                    ),
                    backgroundColor: Color(0xFF4C6FFF),
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    'Send Password Reset Email',
                    style: GoogleFonts.getFont(
                      'Nunito Sans',
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.2,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  bool validate(String text) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(text);
  }
}
