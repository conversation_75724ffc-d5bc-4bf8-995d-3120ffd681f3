{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/flutterProject/flutter_firebase/android/app/.cxx/Debug/4e2g342t/x86", "source": "C:/flutterVersion/3_29_2/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}