import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class CreatePollSrceen extends StatefulWidget {
  String currentUserId;
  CreatePollSrceen({super.key, required this.currentUserId});

  @override
  State<CreatePollSrceen> createState() => _CreatePollSrceenState();
}

class _CreatePollSrceenState extends State<CreatePollSrceen> {
  final titleCTRL = TextEditingController();
  final List<TextEditingController> optionNameController = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
  ];
  final List<File?> _opstionImages = [null, null, null];
  final ImagePicker _imagePicker = ImagePicker();
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("crate poll"), centerTitle: true),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: Colors.grey, width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 2,
                    blurRadius: 5,
                    offset: Offset(0, 3),
                  ),
                ],
              ),
              child: TextFormField(
                controller: titleCTRL,
                decoration: InputDecoration(
                  labelText: "Poll Title",
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
            for (int i = 0; i < 3; i++)
              Container(
                padding: EdgeInsets.all(10),
                margin: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  border: Border.all(color: Colors.grey, width: 1),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 2,
                      blurRadius: 5,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: optionNameController[i],
                        decoration: InputDecoration(
                          labelText: "Option ${i + 1} Name",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 15),
                    GestureDetector(
                      onTap: () {
                        pickImage(i);
                      },
                      child:
                          _opstionImages[i] != null
                              ? GestureDetector(
                                onTap: () => pickImage(i),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: Image.file(
                                    _opstionImages[i]!,
                                    height: 50,
                                    width: 50,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              )
                              : GestureDetector(
                                onTap: () => pickImage(i),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.grey[300],
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  height: 50,
                                  width: 50,
                                  child: Icon(Icons.image),
                                ),
                              ),
                    ),
                  ],
                ),
              ),
            SizedBox(height: 15),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orangeAccent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
              ),
              onPressed: () {
                submitPoll(widget.currentUserId, context);
              },
              child:
                  isLoading
                      ? CircularProgressIndicator()
                      : Text(
                        "Create Poll",
                        style: TextStyle(fontSize: 18, color: Colors.white),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> pickImage(int i) async {
    final pickfile = await _imagePicker.pickImage(source: ImageSource.gallery);
    if (pickfile != null) {
      setState(() {
        _opstionImages[i] = File(pickfile.path);
      });
    }
  }

  Future<void> submitPoll(String userId, BuildContext context) async {
    if (!validateForm(context)) return;
    setState(() {
      isLoading = true;
    });
    try {
      final pollDoc = FirebaseFirestore.instance.collection("polls").doc();
      final pollId = pollDoc.id;

      final List<Map<String, dynamic>> options = [];
      for (int i = 0; i < 3; i++) {
        final imageUr = await uploadImage(_opstionImages[i]!, pollId, i);
        if (imageUr == null) throw Exception('Failed to upload image');
        options.add({
          "name": optionNameController[i].text,
          "image": imageUr,
          "votes": 0,
        });
        await pollDoc.set({
          "pollId": pollId,
          "title": titleCTRL.text.toString(),
          "options": options,
          "userId": userId,
          "createdAt": FieldValue.serverTimestamp(),
          "creatorId": userId,
          "total_votes": 0,
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text("Poll created successfully")));
        Navigator.pop(context);
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(" Failed to create Poll")));
      Navigator.pop(context);
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  bool validateForm(BuildContext context) {
    if (titleCTRL.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text("Please enter poll title")));
      return false;
    }
    for (int i = 0; i < optionNameController.length; i++) {
      if (optionNameController[i].text.isEmpty) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text("Please enter option $i name")));
        return false;
      }
      if (_opstionImages.contains(null)) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Please select option $i image")),
        );
        return false;
      }
    }
    return true;
  }

  Future<String?> uploadImage(File? opstionImag, String pollId, int i) async {
    try {
      final storageRef = FirebaseStorage.instance.ref().child(
        "poll/$pollId${i.toString()}_$pollId.jpg",
      );
      final uploadImage = await storageRef.putFile(opstionImag!);
      return await uploadImage.ref.getDownloadURL();
    } catch (e) {
      print(e);
      return null;
    }
  }
}
