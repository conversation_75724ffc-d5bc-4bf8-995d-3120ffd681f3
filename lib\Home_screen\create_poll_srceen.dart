import 'dart:io';

import 'package:flutter/material.dart';

class CreatePollSrceen extends StatefulWidget {
  const CreatePollSrceen({super.key});

  @override
  State<CreatePollSrceen> createState() => _CreatePollSrceenState();
}

class _CreatePollSrceenState extends State<CreatePollSrceen> {
  final titleCTRL = TextEditingController();
  final List<TextEditingController> optionNameController = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
  ];
  final List<File?> _opstionImages = [null, null, null];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("crate poll"), centerTitle: true),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: Colors.grey, width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 2,
                    blurRadius: 5,
                    offset: Offset(0, 3),
                  ),
                ],
              ),
              child: TextFormField(
                controller: titleCTRL,
                decoration: InputDecoration(
                  labelText: "Poll Title",
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
            for (int i = 0; i < 3; i++)
              Container(
                margin: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  border: Border.all(color: Colors.grey, width: 1),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 2,
                      blurRadius: 5,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    TextFormField(
                      controller: optionNameController[i],
                      decoration: InputDecoration(
                        labelText: "Option ${i + 1} Name",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                    const SizedBox(width: 15),
                    _opstionImages[i] != null
                        ? ClipRRect(
                          borderRadius: BorderRadius.circular(10),
                          child: Image.file(
                            _opstionImages[i]!,
                            height: 50,
                            width: 50,
                            fit: BoxFit.cover,
                          ),
                        )
                        : Container(
                          height: 50,
                          width: 50,
                          color: Colors.grey[300],
                          child: Icon(Icons.image),
                        ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
