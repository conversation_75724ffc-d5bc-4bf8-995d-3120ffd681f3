// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDh3v0VADFaW0U8YTHSu_WCgwsDerFZbQM',
    appId: '1:278895796126:web:bb5422239830eec022c08f',
    messagingSenderId: '278895796126',
    projectId: 'flutter-course-e4159',
    authDomain: 'flutter-course-e4159.firebaseapp.com',
    storageBucket: 'flutter-course-e4159.firebasestorage.app',
    measurementId: 'G-NJ9FE7N5GQ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDYTOXVWF665O9luQxUrSgWrh-axQFGgmA',
    appId: '1:278895796126:android:84b0358a7d1bdb7922c08f',
    messagingSenderId: '278895796126',
    projectId: 'flutter-course-e4159',
    storageBucket: 'flutter-course-e4159.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyB6quvvrb-q4aleQVKb9JY7NSkKaUpJVB8',
    appId: '1:278895796126:ios:67e2f1e0c64b10cf22c08f',
    messagingSenderId: '278895796126',
    projectId: 'flutter-course-e4159',
    storageBucket: 'flutter-course-e4159.firebasestorage.app',
    iosBundleId: 'com.example.flutterFirebase',
  );
}
