{"buildFiles": ["C:\\flutterVersion\\3_29_2\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterProject\\flutter_firebase\\android\\app\\.cxx\\Debug\\4e2g342t\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterProject\\flutter_firebase\\android\\app\\.cxx\\Debug\\4e2g342t\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}