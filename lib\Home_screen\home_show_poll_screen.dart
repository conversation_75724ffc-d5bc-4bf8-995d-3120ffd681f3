import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_firebase/Home_screen/create_poll_srceen.dart';
import 'package:flutter_firebase/service/AuthServices/auth_services.dart';

class HomeShowPollScreen extends StatefulWidget {
  const HomeShowPollScreen({super.key});

  @override
  State<HomeShowPollScreen> createState() => _HomeShowPollScreenState();
}

class _HomeShowPollScreenState extends State<HomeShowPollScreen> {
  final String _currentUserId = FirebaseAuth.instance.currentUser!.uid;

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: Text("CreatePoll"),
          centerTitle: true,
          actions: [
            IconButton(
              onPressed: () {
                AuthServices.signOut();
              },
              icon: Icon(Icons.logout),
            ),
          ],
          bottom: TabBar(
            indicatorColor: Colors.orange,
            unselectedLabelColor: Colors.black,
            indicatorSize: TabBarIndicatorSize.tab,
            labelStyle: TextStyle(
              color: Colors.orange,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ), // TextStyle
            tabs: [
              Tab(text: "All"),
              Tab(text: "Posted"), // Tab
              Tab(text: "Voted"), // TabBar
            ],
          ),
        ),
        body: TabBarView(
          children: [
            Container(child: Center(child: Text("All"))),
            Container(child: Center(child: Text("Posted Poll"))), // Container
            Container(child: Center(child: Text("Voted Poll"))),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) =>
                        CreatePollSrceen(currentUserId: _currentUserId),
              ),
            );
          },
          child: Icon(Icons.add),
        ),
      ),
    );
  }
}
