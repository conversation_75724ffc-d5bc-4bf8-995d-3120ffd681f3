import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_firebase/Home_screen/create_poll_srceen.dart';
import 'package:flutter_firebase/Home_screen/home_show_poll_screen.dart';
import 'firebase_options.dart' if (dart.library.js_dev) 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // ຕິດຕັ້ງ Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  // This widget is the root of your application.

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const HomeShowPollScreen(),
    );
  }
}
