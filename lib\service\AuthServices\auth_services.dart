import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';

class AuthServices {
  static Future<String> signUpwithEmail(String email, String password) async {
    try {
      await FirebaseAuth.instance.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      return "Signed Up Successfully"; // Return a success message
    } on FirebaseAuthException catch (e) {
      if (e.code == 'weak-password') {
        print('The password provided is too weak.');
        return 'The password provided is too weak.';
      } else if (e.code == 'email-already-in-use') {
        print('The account already exists for that email.');
        return 'The account already exists for that email.';
      }
      return 'FirebaseAuthException: ${e.message}';
    } catch (e) {
      print(e);
      return 'An error occurred: $e';
    }
  }

  static hanaleSignUp(
    String email,
    String password,
    BuildContext context,
  ) async {
    String message = await signUpwithEmail(email, password);
    showSnackBar(message, context);
  }

  static Future<String> signinwithEmail(String email, String password) async {
    try {
      await FirebaseAuth.instance.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return "Sign in Successfully"; // Return a success message
    } catch (e) {
      print(e);
      return 'An error occurred: $e';
    }
  }

  static hanaleSignin(
    String email,
    String password,
    BuildContext context,
  ) async {
    String message = await signinwithEmail(email, password);
    showSnackBar(message, context);
  }

  static void showSnackBar(String message, BuildContext context) {
    final snackBar = SnackBar(
      content: Text(message, style: TextStyle(color: Colors.white)),
      backgroundColor: Colors.orange,
      behavior: SnackBarBehavior.floating,
      duration: Duration(seconds: 3),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  // Google Sign-In method using the new v7 API
  static Future<Map<String, dynamic>?> siginwithGoogle() async {
    try {
      // Initialize GoogleSignIn if needed
      await GoogleSignIn.instance.initialize();

      // Authenticate with Google
      final GoogleSignInAccount googleUser = await GoogleSignIn.instance
          .authenticate(scopeHint: ['email']);

      // Get authorization for Firebase scopes
      final authClient = GoogleSignIn.instance.authorizationClient;
      final authorization = await authClient.authorizationForScopes(['email']);

      // Get authentication tokens
      final GoogleSignInAuthentication googleAuth = googleUser.authentication;

      // Create Firebase credential
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: authorization?.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase
      UserCredential userCredential = await FirebaseAuth.instance
          .signInWithCredential(credential);

      print('Successfully signed in: ${userCredential.user?.email}');
      return {
        'user': userCredential.user?.email,
        'displayName': userCredential.user?.displayName,
      };
    } on GoogleSignInException catch (e) {
      print('Google Sign In error: ${e.code.name} - ${e.description}');
      return null;
    } catch (e) {
      print('Error signing in with Google: $e');
      return null;
    }
  }

  static Future<void> signOut() async {
    await GoogleSignIn.instance.disconnect();
    await FirebaseAuth.instance.signOut();
  }
}
